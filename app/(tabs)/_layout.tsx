import React from 'react';
import {useColorScheme} from '@/hooks/use-color-scheme';
import {Label, NativeTabs} from "expo-router/unstable-native-tabs";

export default function TabLayout() {
    const colorScheme = useColorScheme();

    return (
        <NativeTabs
            backgroundColor="black"
        >
            <NativeTabs.Trigger name="page">
                <NativeTabs.Trigger.TabBar
                    backgroundColor="white"
                />
                <Label>Page</Label>
            </NativeTabs.Trigger>
        </NativeTabs>
    );
}
